from app.agent_1.ruh_global_agent import run_global_stream, run_global
import os


async def main():
    user_input = input("Enter query: ")
    provider = input("Enter provider (e.g., anthropic, openai): ") or "anthropic"
    model = (
        input("Enter model (e.g., claude-sonnet-4.5, gpt-4o-mini): ")
        or "claude-sonnet-4.5"
    )
    user_id = input("Enter user_id: ") or "test_user"
    conversation_id = input("Enter conversation_id: ") or "test_conversation"
    agent_id = input("Enter agent_id (optional, press enter to skip): ") or None
    use_memory_cross_conversations_input = input("Use memory across conversations? (y/n, default: y): ").lower()
    use_memory_cross_conversations = use_memory_cross_conversations_input != "n"
    use_kb = input("Use knowledge base? (y/n): ").lower() == "y"
    use_search = input("Use web search? (y/n): ").lower() == "y"
    use_stream = input("Use streaming? (y/n): ").lower() == "y"

    save_to_file = input("Save output to file? (y/n): ").lower() == "y"
    file_name = None

    if save_to_file:
        user_file_name = input("Enter file name (default: output.txt): ").strip()
        if not user_file_name:
            file_name = "output.txt"
        else:
            base_name, ext = os.path.splitext(user_file_name)
            if ext.lower() == ".txt":
                file_name = user_file_name
            else:
                file_name = base_name + ".txt"

    if use_stream:
        if file_name:
            with open(file_name, "w") as f:
                async for chunk in run_global_stream(
                    user_message=user_input,
                    provider=provider,
                    model=model,
                    use_knowledge_base=use_kb,
                    use_search=use_search,
                    user_id=user_id,
                    conversation_id=conversation_id,
                    agent_id=agent_id,
                    use_memory_cross_conversations=use_memory_cross_conversations,
                ):
                    f.write(str(chunk) + "\n")
                    print(chunk)
            print(f"\nOutput saved to {file_name}")
        else:
            async for chunk in run_global_stream(
                user_message=user_input,
                provider=provider,
                model=model,
                use_knowledge_base=use_kb,
                use_search=use_search,
                user_id=user_id,
                conversation_id=conversation_id,
                agent_id=agent_id,
                use_memory_cross_conversations=use_memory_cross_conversations,
            ):
                print(chunk)

    else:
        result = await run_global(
            user_message=user_input,
            provider=provider,
            model=model,
            use_knowledge_base=use_kb,
            use_search=use_search,
            user_id=user_id,
            conversation_id=conversation_id,
            agent_id=agent_id,
            use_memory_cross_conversations=use_memory_cross_conversations,
        )
        print("\n=== RESULT ===")
        print(result)
        
        if file_name:
            with open(file_name, "w") as f:
                f.write(str(result))
            print(f"\nOutput saved to {file_name}")


if __name__ == "__main__":
    import asyncio

    asyncio.run(main())
