import asyncio
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(__file__)), "app"))

from app.agent.global_agent import run_agent_stream
from app.utils.file_processor import get_file_extension


async def test_attachments_agent():
    """
    Test function to run the agent with image and document attachments and print chunks
    """
    message = "tell about this doc in one line"
    attachments = [
        # {
        #     "file_name": "jpeg-test.jpeg",
        #     "file_size": 68733,
        #     "file_url": "https://storage.googleapis.com/ruh-dev/chat-uploads/1758521759-1758521758243-annie-spratt-xpkTvptEbRw-unsplashMedium.jpeg",
        # },
        # {
        #     "file_name": "sample-png.png",
        #     "file_size": 512596,
        #     "file_url": "https://storage.googleapis.com/ruh-dev/chat-uploads/1758562770-1758562769506-sample-png.png",
        # },
        # {
        #     "file_name": "sample-jpg.jpg",
        #     "file_size": 102117,
        #     "file_url": "https://storage.googleapis.com/ruh-dev/chat-uploads/1758562770-1758562769506-sample-jpg.jpg",
        # },
        # {
        #     "file_name": "MOCK_DATA.csv",
        #     "file_size": 105337,
        #     "file_url": "https://storage.googleapis.com/ruh-dev/chat-uploads/1758536619-1758536618448-MOCK_DATA.csv",
        # },
        # {
        #     "file_name": "example.txt",
        #     "file_size": 105337,
        #     "file_url": "https://example-files.online-convert.com/document/txt/example.txt",
        # },
        # {
        #     "file_name": "example.md",
        #     "file_size": 105337,
        #     "file_url": "https://gist.githubusercontent.com/rt2zz/e0a1d6ab2682d2c47746950b84c0b6ee/raw/83b8b4814c3417111b9b9bef86a552608506603e/markdown-sample.md",
        # },
        # {
        #     "file_name": "Ruh.pdf",
        #     "file_size": 102490,
        #     "file_url": "https://storage.googleapis.com/ruh-dev/chat-uploads/1758536619-1758536618448-Ruh.pdf",
        # },
        # {
        #     "file_name": "Arcadia_Campaign_Testing_Updated.docx",
        #     "file_size": 37676,
        #     "file_url": "https://storage.googleapis.com/ruh-dev/chat-uploads/1758536619-1758536618448-Arcadia_Campaign_Testing_Updated.docx",
        # },
    ]

    print(f"Running agent with message: {message}")
    print(f"With {len(attachments)} attachments")

    # Log the file types and extensions extracted from URLs
    print("\nFile types and extensions extracted from URLs:")
    for att in attachments:
        file_info = get_file_extension(att["file_url"])
        print(
            f"  {att['file_name']} -> Type: '{file_info['type']}', Extension: '{file_info['extension']}'"
        )

    print("-" * 50)

    async for chunk in run_agent_stream(message, attachments=attachments):
        print(chunk)

    print("\n" + "-" * 50)
    print("Agent response completed")


if __name__ == "__main__":
    asyncio.run(test_attachments_agent())
