# OpenAI API Key
OPENAI_API_KEY=your-openai-api-key-here
LLM_MODEL=gpt-4o-mini

ENV=dev

# Model Provider Selection (requesty, openrouter, openai)
MODEL_PROVIDER=requesty

# Requesty AI Router Configuration (for unified LLM access)
REQUESTY_API_KEY=your-requesty-api-key-here
REQUESTY_BASE_URL=https://router.requesty.ai/v1

# OpenRouter Configuration (alternative to Requesty)
OPENROUTER_API_KEY=your-openrouter-api-key-here
OPENROUTER_MODEL=openai/gpt-4o-mini
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
OPENROUTER_SITE_URL=your-site-url-here
OPENROUTER_SITE_NAME=your-site-name-here


# Kafka configuration
KAFKA_BOOTSTRAP_SERVERS=localhost:9094
# KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_AGENT_CREATION_TOPIC=agent_creation_requests
KAFKA_AGENT_CHAT_TOPIC=agent_chat_requests
KAFKA_AGENT_RESPONSE_TOPIC=agent_chat_responses
KAFKA_CONSUMER_GROUP=autogen-agent-service-group
KAFKA_AGENT_QUERY_TOPIC=agent_query_requests
KAFKA_AGENT_MESSAGE_TOPIC=agent_message_requests
KAFKA_AGENT_SESSION_DELETION_TOPIC=agent_session_deletion_requests
KAFKA_ORCHESTRATION_TEAM_SESSION_TOPIC=orchestration_team_session_requests
KAFKA_ORCHESTRATION_TEAM_CHAT_TOPIC=orchestration_team_chat_requests
KAFKA_HUMAN_INPUT_REQUEST_TOPIC=human_input_requests
KAFKA_HUMAN_INPUT_RESPONSE_TOPIC=human_input_responses
KAFKA_AGENT_CHAT_STOP_TOPIC=agent_chat_stop_requests
KAFKA_TOKEN_USAGE_TOPIC=token-usage-events

# Redis configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# API Gateway
API_GATEWAY_URL=https://app-dev.rapidinnovation.dev/api/v1
API_GATEWAY_KEY=your_api_gateway_key_here
API_GATEWAY_ORGANIZATION_KEY=your_api_gateway_organization_key_here

# Workflow API Gateway
WORKFLOW_API_GATEWAY_URL=https://ruh-execution.rapidinnovation.dev/api/v1
WORKFLOW_API_GATEWAY_KEY=your_workflow_api_key_here

# Pinecone Vector Database
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_ENVIRONMENT=us-east-1
PINECONE_INDEX_NAME=agent-memory
PINECONE_DIMENSION=1536
PINECONE_METRIC=cosine
PINECONE_CLOUD=aws


EXA_API_KEY=your_exa_api_key_here

# Knowledge Base Configuration
KNOWLEDGE_BASE_TOP_K=10

ORCHESTRATION_TEAM_CHAT_MODEL_ID=ac0f0902-0b68-4103-8494-87595ea5d55b


QDRANT_HOST=qdrant-db.rapidinnovation.dev
QDRANT_PORT=443
QDRANT_API_KEY=<qdrant-api-key>
QDRANT_COLLECTION_NAME=local_agent_memory
QDRANT_VECTOR_SIZE=384
QDRANT_DISTANCE=Cosine
QDRANT_TIMEOUT=60


AGENT_GATEWAY_URL=http://localhost:8001/api/v1
AGENT_GATEWAY_API_KEY=XXXX
