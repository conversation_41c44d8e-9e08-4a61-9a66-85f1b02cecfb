from abc import ABC
from typing import List, Optional, Any, Dict
from app.utils.file_processor import process_attachments_for_agent


class BaseProvider(ABC):
    """
    Base class for agent providers.
    Provides a common interface for agent initialization and streaming responses.
    """

    def __init__(self, tools=None, model=None, system_prompt=None):
        """
        Initialize the provider with tools and model configuration.
        Args:
            tools: List of tools to be used by the agent
            model: Model configuration for the agent
        """
        from strands import Agent
        from app.services.model_config import get_default_model

        self.model = model or get_default_model()
        self.agent = Agent(
            tools=tools or [], model=self.model, system_prompt=system_prompt
        )

    async def run_agent_stream(
        self, message: str, attachments: Optional[List[Dict[str, Any]]] = None
    ):
        """
        Generator function that yields formatted chunks for agent responses.
        Args:
            message: User message to be processed
            attachments: Optional list of attachment dicts with file_url and file_type

        Yields:
            Formatted event chunks
        """
        from app.services.stream_formatter import format_stream_chunk

        content: List[Any] = [{"text": message}]

        if attachments:
            attachment_content = await process_attachments_for_agent(attachments)
            content.extend(attachment_content)

        response = self.agent.stream_async(content)
        async for event in response:
            formatted_event = format_stream_chunk(event)
            if formatted_event:
                yield formatted_event

    def run_agent(self, message: str):
        """
        Runs agent and returns result directly without streaming.
        Args:
            message: User message to be processed

        Returns:
            Agent result
        """

        response = self.agent(message)
        return response
