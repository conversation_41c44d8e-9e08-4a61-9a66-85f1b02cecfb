from strands import tool, Agent

import asyncio
import json
import os
import uuid
from enum import Enum
from typing import Dict, List, Any, Optional

from app.agent.tools.todo_read import TODO_READ_PROMPT
from app.agent.tools.todo_write import TODO_WRITE_PROMPT
from app.services.model_config import get_default_model
from app.helper.redis_client import RedisClient
from strands_tools import current_time


class TodoStatus(Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class TodoManager:
    def __init__(self, file_path: str = "session_todos.json"):
        self.file_path = file_path
        self.folder_path = "static/todos"
    
    def _load_todos(self) -> Dict[str, Any]:
        """Load todos from the external file"""
        # check folder exists, if not create it
        if not os.path.exists(self.folder_path):
            os.makedirs(self.folder_path)

        if not os.path.exists(f"{self.folder_path}/{self.file_path}"):
            return {"todos": []}
        
        try:
            with open(f"{self.folder_path}/{self.file_path}", 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, IOError):
            return {"todos": []}
    
    def _save_todos(self, todos_data: Dict[str, Any]) -> None:
        """Save todos to the external file"""
        try:
            with open(f"{self.folder_path}/{self.file_path}", 'w', encoding='utf-8') as f:
                json.dump(todos_data, f, indent=2, ensure_ascii=False)
        except IOError as e:
            raise Exception(f"Failed to save todos: {str(e)}")
    
    def get_todos(self) -> List[Dict[str, Any]]:
        """Get all todos"""
        data = self._load_todos()
        return data.get("todos", [])
    
    def update_todos(self, todos: List[Dict[str, Any]]) -> None:
        """Update the entire todo list"""
        # Validate each todo item
        for todo in todos:
            if not all(key in todo for key in ["id", "content", "status", "priority"]):
                raise ValueError("Each todo must have id, content, status, and priority")
            
            # Validate status
            try:
                TodoStatus(todo["status"])
            except ValueError:
                raise ValueError(f"Invalid status: {todo['status']}. Must be one of: {[s.value for s in TodoStatus]}")
        
        data = {"todos": todos}
        self._save_todos(data)


class RedisTodoManager:
    """Redis-based todo manager that provides the same interface as TodoManager"""

    def __init__(self, session_id: str = "default", redis_client: Optional[RedisClient] = None):
        self.session_id = session_id
        self.redis_client = redis_client
        self._redis_key = f"todos:default_test"  #TODO

    def _run_async(self, coro_func):
        """Helper method to run async operations in sync context"""
        import concurrent.futures

        def run_in_thread():
            # Create a new event loop and Redis client for this thread
            new_loop = asyncio.new_event_loop()
            asyncio.set_event_loop(new_loop)
            try:
                # Create a fresh Redis client for this operation
                fresh_redis_client = RedisClient()
                # Execute the coroutine with the fresh client
                return new_loop.run_until_complete(coro_func(fresh_redis_client))
            finally:
                new_loop.close()
                asyncio.set_event_loop(None)

        # Always run in a separate thread to avoid event loop conflicts
        with concurrent.futures.ThreadPoolExecutor() as executor:
            future = executor.submit(run_in_thread)
            return future.result()

    async def _ensure_redis_connection(self, redis_client=None):
        """Ensure Redis connection is initialized"""
        client = redis_client or self.redis_client
        if client is None:
            client = RedisClient()
        await client.initialize()
        return client

    async def _load_todos_async(self, redis_client=None) -> Dict[str, Any]:
        """Load todos from Redis"""
        try:
            client = await self._ensure_redis_connection(redis_client)
            data = await client.get_value(self._redis_key)
            if data is None:
                return {"todos": []}

            # If data is already a dict (from Redis deserialization), return it
            if isinstance(data, dict):
                return data

            # If data is a string, try to parse it as JSON
            if isinstance(data, str):
                try:
                    return json.loads(data)
                except json.JSONDecodeError:
                    return {"todos": []}

            return {"todos": []}
        except Exception as e:
            # Log error and return empty todos list as fallback
            print(f"Error loading todos from Redis: {e}")
            return {"todos": []}

    async def _save_todos_async(self, todos_data: Dict[str, Any], redis_client=None) -> None:
        """Save todos to Redis"""
        try:
            client = await self._ensure_redis_connection(redis_client)
            print(self._redis_key, todos_data)
            await client.set_value(self._redis_key, todos_data, ttl=3600)
        except Exception as e:
            raise Exception(f"Failed to save todos to Redis: {str(e)}")

    def get_todos(self) -> List[Dict[str, Any]]:
        """Get all todos"""
        data = self._run_async(lambda client: self._load_todos_async(client))
        return data.get("todos", [])

    def update_todos(self, todos: List[Dict[str, Any]]) -> None:
        """Update the entire todo list"""
        # Validate each todo item (same validation as original TodoManager)
        for todo in todos:
            if not all(key in todo for key in ["id", "content", "status", "priority"]):
                raise ValueError("Each todo must have id, content, status, and priority")

            # Validate status
            try:
                TodoStatus(todo["status"])
            except ValueError:
                raise ValueError(f"Invalid status: {todo['status']}. Must be one of: {[s.value for s in TodoStatus]}")

        data = {"todos": todos}
        self._run_async(lambda client: self._save_todos_async(data, client))


# Global instance - using Redis by default
todo_manager = RedisTodoManager()


@tool(name="todo_read", description=TODO_READ_PROMPT)
def todo_read() -> str:
    """This tool is used to read and get the todo list for the session"""
    try:
        todos = todo_manager.get_todos()
        
        if not todos:
            return json.dumps({
                "message": "No todos found for this session",
                "todos": []
            }, indent=2)
        
        return json.dumps({
            "todos": todos,
            "summary": {
                "total": len(todos),
                "pending": len([t for t in todos if t["status"] == "pending"]),
                "in_progress": len([t for t in todos if t["status"] == "in_progress"]),
                "completed": len([t for t in todos if t["status"] == "completed"]),
                "cancelled": len([t for t in todos if t["status"] == "cancelled"])
            }
        }, indent=2)
        
    except Exception as e:
        return json.dumps({
            "error": f"Failed to read todos: {str(e)}",
            "todos": []
        }, indent=2)


@tool(name="todo_write", description=TODO_WRITE_PROMPT)
def todo_write(todos: List[Dict[str, Any]]) -> str:
    """This tool is used to write and update the todo list for the session
    
    Args:
        todos: List of todo items, each with:
            - id: Unique identifier (string)
            - content: Description of the task (string)
            - status: Current status - one of: pending, in_progress, completed, cancelled
            - priority: Priority level (string) - typically: low, medium, high, urgent
    """
    try:
        # Generate IDs for todos that don't have them
        for todo in todos:
            if "id" not in todo or not todo["id"]:
                todo["id"] = str(uuid.uuid4())
        
        # Update the todo list
        todo_manager.update_todos(todos)
        
        return json.dumps({
            "message": "Todos updated successfully",
            "total_todos": len(todos),
            "todos": todos
        }, indent=2)
        
    except Exception as e:
        return json.dumps({
            "error": f"Failed to update todos: {str(e)}",
            "todos": []
        }, indent=2)



@tool(name="todo_agent_tool", description="A todo agent tool that can handle todo-related requests. Use this tool when users want to manage their todo list with natural language.")
def mark_todo_task_done(index: int) -> str:
    """Mark a todo task as done by its index (1-based)"""
    try:
        todos = todo_manager.get_todos()
        if index < 1 or index > len(todos):
            return f"Error: Invalid index {index}. Valid range is 1-{len(todos)}"

        todo_index = index - 1
        todos[todo_index]["status"] = "completed"
        todo_manager.update_todos(todos)
        return f"Marked as done: {todos[todo_index]['content']}"
    except Exception as e:
        return f"Error: {str(e)}"


# Create the todo agent
def create_todo_agent():
    """Create and return a todo agent with the necessary tools"""
    model = get_default_model()
    return Agent(
        tools=[todo_read, todo_write, mark_todo_task_done, current_time],
        model=model,
        system_prompt=(
            "You are a TODO assistant. You can add tasks, list all tasks, and mark tasks as done. "
            "Reply with the result of the requested operation."
        )
    )


@tool
def todo_agent_tool(message: str) -> str:
    """
    A todo agent tool that can handle todo-related requests.
    Use this tool when users want to manage their todo list with natural language.

    Args:
        message: The user's message about todo management (e.g., "Add 'Write report for project Alpha'", "List all TODOs", "Mark task 1 as done")

    Returns:
        The response from the todo agent
    """
    try:
        agent = create_todo_agent()
        response = agent(message)
        return response
    except Exception as e:
        return f"Error in todo agent: {str(e)}"
