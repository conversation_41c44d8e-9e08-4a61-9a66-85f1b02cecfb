from typing import Optional, List, Dict, Any
from strands_tools import image_reader, calculator, current_time
from app.agent.base_provider import BaseProvider
from app.agent.global_agent_prompt import GLOBAL_AGENT_PROMPT
from app.agent.tools.web_search import web_search
from app.services.model_config import get_model
from app.agent.tools.todo import todo_agent_tool


class GlobalAgent(BaseProvider):
    """
    Global agent provider that uses python_repl and calculator tools
    """

    def __init__(self):
        """
        Initialize the global agent with internal tools
        """
        super().__init__(
            tools=[web_search, todo_agent_tool, image_reader, current_time, calculator]
        )


global_agent = GlobalAgent()


async def run_agent_stream(
    message: str,
    provider: Optional[str] = None,
    model_name: Optional[str] = None,
    attachments: Optional[List[Dict[str, Any]]] = None,
):
    """
    Generator function that yields formatted chunks for agent responses
    """
    from app.services.model_config import DEFAULT_PROVIDER, DEFAULT_MODEL_NAME

    provider = provider or DEFAULT_PROVIDER
    model_name = model_name or DEFAULT_MODEL_NAME
    model = get_model(provider, model_name)
    agent = BaseProvider(
        tools=[web_search, todo_agent_tool, image_reader, current_time, calculator],
        model=model,
        system_prompt=GLOBAL_AGENT_PROMPT,
    )
    async for chunk in agent.run_agent_stream(message, attachments):
        yield chunk


def run_agent(
    message: str, provider: Optional[str] = None, model_name: Optional[str] = None
):
    """
    Runs agent and returns result directly without streaming.
    Args:
        message: User message to be processed

    Returns:
        Agent result
    """
    from app.services.model_config import DEFAULT_PROVIDER, DEFAULT_MODEL_NAME

    provider = provider or DEFAULT_PROVIDER
    model_name = model_name or DEFAULT_MODEL_NAME
    model = get_model(provider, model_name)
    agent = BaseProvider(
        tools=[web_search, todo_agent_tool, calculator, current_time],
        model=model,
        system_prompt=GLOBAL_AGENT_PROMPT,
    )
    response = agent.run_agent(message)
    return response


async def test_agent():
    """
    Test function to run the agent with user input and print chunks
    """
    user_input = input("Enter your message: ")
    print(f"Running agent with input: {user_input}")
    print("-" * 50)

    # result = run_agent(user_input)
    # print(result)
    # print(f"Total tokens: {result.metrics.accumulated_usage}")
    # print(f"Execution time: {sum(result.metrics.cycle_durations):.2f} seconds")
    # print(f"Tools used: {list(result.metrics.tool_metrics.keys())}")

    async for chunk in run_agent_stream(user_input):
        print(chunk)


# async for chunk in run_agent_stream(user_input):
#     print(chunk)
#
# print("\n" + "-" * 50)
# print("Agent response completed")


if __name__ == "__main__":
    import asyncio

    asyncio.run(test_agent())
