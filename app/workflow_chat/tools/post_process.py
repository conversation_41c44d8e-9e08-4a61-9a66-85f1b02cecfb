import json
import math
import os
from typing import Any, Dict, List, Optional, Tu<PERSON>

import numpy as np
import requests

# Constants for better performance and readability
EPSILON = 1e-6
DEFAULT_RANDOM_DISPLACEMENT = 1.0


def calculate_node_size(inputs: int, outputs: int) -> tuple[int, int]:
    width = 208
    base_height = 90
    total_handles = inputs + outputs

    if total_handles <= 2:
        spacing_per_handle = 35
    elif total_handles == 3:
        spacing_per_handle = 25
    else:
        spacing_per_handle = 20

    height = base_height + total_handles * spacing_per_handle
    return width, height


def calculate_repulsive_force(
    rect1: Dict, rect2: Dict, separation: float, strength: float
) -> Tuple[float, float]:
    """
    Calculate repulsive force between two rectangles.

    Args:
        rect1, rect2: Rectangle dictionaries with 'x', 'y', 'w', 'h'
        separation: Minimum separation distance
        strength: Force strength multiplier

    Returns:
        Tuple of (force_x, force_y) applied to rect1
    """
    # Calculate centers (optimized with single calculation)
    center1_x = rect1["x"] + rect1["w"] * 0.5
    center1_y = rect1["y"] + rect1["h"] * 0.5
    center2_x = rect2["x"] + rect2["w"] * 0.5
    center2_y = rect2["y"] + rect2["h"] * 0.5

    # Calculate distance between centers
    dx = center1_x - center2_x
    dy = center1_y - center2_y
    distance_squared = dx * dx + dy * dy
    distance = math.sqrt(distance_squared)

    if distance < EPSILON:  # Very small distance, avoid division by zero
        # Apply small random displacement
        dx = np.random.uniform(-1, 1)
        dy = np.random.uniform(-1, 1)
        distance = DEFAULT_RANDOM_DISPLACEMENT

    # Calculate minimum distance needed based on rectangle dimensions
    min_distance_x = (rect1["w"] + rect2["w"]) * 0.5 + separation
    min_distance_y = (rect1["h"] + rect2["h"]) * 0.5 + separation

    # Use the larger of the two minimum distances to ensure proper separation
    min_distance = max(min_distance_x, min_distance_y)

    # Only apply force if rectangles are too close
    if distance < min_distance:
        # Calculate force magnitude - use a more controlled approach
        overlap = min_distance - distance
        force_magnitude = strength * overlap

        # Normalize direction vector (avoid division by zero already handled above)
        inv_distance = 1.0 / distance
        dx_norm = dx * inv_distance
        dy_norm = dy * inv_distance

        # Calculate force components
        force_x = force_magnitude * dx_norm
        force_y = force_magnitude * dy_norm

        return force_x, force_y

    return 0.0, 0.0


def rectangle_layout_with_separation(
    rectangles: List[Dict],
    fixed_node_id: Optional[str] = None,
    separation: float = 20.0,
    max_iterations: int = 500,
    convergence_threshold: float = 0.5,
    force_strength: float = 0.2,
    damping: float = 0.85,
    step_size: float = 2.0,
) -> List[Dict]:
    """
    Arrange rectangles with no overlap and fixed separation using force-directed layout.

    Args:
        rectangles: List of dictionaries with keys 'id', 'x', 'y', 'w', 'h'
        fixed_node_id: ID of the node to keep fixed in position (optional)
        separation: Minimum separation distance between rectangles (default: 20.0)
        max_iterations: Maximum number of iterations for the algorithm (default: 500)
        convergence_threshold: Threshold for convergence (default: 0.5)
        force_strength: Strength of repulsive forces (default: 0.2)
        damping: Damping factor to reduce oscillations (default: 0.85)
        step_size: Maximum step size per iteration (default: 2.0)

    Returns:
        List of rectangles with updated positions
    """
    if not rectangles:
        return rectangles

    # Create a copy to avoid modifying the original
    nodes = [rect.copy() for rect in rectangles]
    num_nodes = len(nodes)

    # Find the fixed node index (optimized lookup)
    fixed_index = None
    if fixed_node_id:
        node_id_to_index = {node["id"]: i for i, node in enumerate(nodes)}
        fixed_index = node_id_to_index.get(fixed_node_id)

    # Initialize velocities as numpy arrays for better performance
    velocities = np.zeros((num_nodes, 2), dtype=np.float64)

    # Pre-calculate step_size_squared for optimization
    step_size_squared = step_size * step_size

    for _ in range(max_iterations):
        # Initialize forces array
        forces = np.zeros((num_nodes, 2), dtype=np.float64)
        max_movement = 0.0

        # Calculate repulsive forces between all pairs of rectangles
        for i in range(num_nodes):
            for j in range(i + 1, num_nodes):
                force_x, force_y = calculate_repulsive_force(
                    nodes[i], nodes[j], separation, force_strength
                )

                forces[i, 0] += force_x
                forces[i, 1] += force_y
                forces[j, 0] -= force_x
                forces[j, 1] -= force_y

        # Update positions based on forces
        for i in range(num_nodes):
            if fixed_index is not None and i == fixed_index:
                continue  # Skip the fixed node

            # Update velocity with damping
            velocities[i, 0] = velocities[i, 0] * damping + forces[i, 0]
            velocities[i, 1] = velocities[i, 1] * damping + forces[i, 1]

            # Limit step size to prevent explosive movement (optimized)
            velocity_magnitude_squared = velocities[i, 0] ** 2 + velocities[i, 1] ** 2
            if velocity_magnitude_squared > step_size_squared:
                velocity_magnitude = math.sqrt(velocity_magnitude_squared)
                scale_factor = step_size / velocity_magnitude
                velocities[i, 0] *= scale_factor
                velocities[i, 1] *= scale_factor

            # Update position
            old_x, old_y = nodes[i]["x"], nodes[i]["y"]
            nodes[i]["x"] += velocities[i, 0]
            nodes[i]["y"] += velocities[i, 1]

            # Calculate movement for convergence check (optimized)
            dx = nodes[i]["x"] - old_x
            dy = nodes[i]["y"] - old_y
            movement = math.sqrt(dx * dx + dy * dy)
            max_movement = max(max_movement, movement)

        # Check for convergence
        if max_movement < convergence_threshold:
            break

    return nodes


# Component type exceptions mapping
COMPONENT_TYPE_EXCEPTIONS = {
    "AgenticAI": "agent",
    "LoopNode": "loop",
}

# Default node dimensions
DEFAULT_NODE_DIMENSIONS = {
    "width": 208,
    "height": 194,
    "position": {"x": 1760, "y": 1960},
}

COMPONENT_URL = os.environ.get("COMPONENT_URL")
MCP_URL = os.environ.get("MCP_URL").format("")
WORKFLOW_URL = os.environ.get("WORKFLOW_URL").format("")


def fulfill_component(
    node_info: dict,
    # api_url: str = "https://app-dev.rapidinnovation.dev/api/v1/components",
    api_url: str = COMPONENT_URL,
) -> Dict[str, Any]:
    """
    Generate a complete workflow node structure for the given component.

    Args:
        node_info (dict): Node information containing OriginalType, node_id, etc.
        api_url (str): API endpoint URL for components

    Returns:
        dict: Complete node structure with definition from API or error dict
    """
    try:
        response = requests.get(api_url)
        response.raise_for_status()
        components_data = response.json()

        # Find component definition across all categories
        component_def = _find_component_definition(
            components_data, node_info["OriginalType"]
        )

        if not component_def:
            return _create_component_error_response(
                components_data, node_info["OriginalType"]
            )

        # Create and configure node structure
        node_structure = _create_base_node_structure(node_info, component_def)
        _apply_node_configuration(node_structure, node_info)
        _apply_default_input_values(node_structure, node_info)

        return node_structure

    except requests.exceptions.RequestException as e:
        return {"error": f"API request failed: {str(e)}"}
    except json.JSONDecodeError as e:
        return {"error": f"Failed to parse API response: {str(e)}"}
    except Exception as e:
        return {"error": f"An error occurred: {str(e)}"}


def _find_component_definition(components_data: dict, node_name: str) -> dict:
    """Find component definition by searching through all categories."""
    for _, components in components_data.items():
        if node_name in components:
            return components[node_name]
    return None


def _create_component_error_response(components_data: dict, node_name: str) -> dict:
    """Create error response with available components list."""
    available_components = []
    for _, components in components_data.items():
        available_components.extend(components.keys())

    error_msg = f"Component '{node_name}' not found in API response.\n"
    error_msg += f"Available components ({len(available_components)}):\n"
    error_msg += "\n".join(
        [f"  - {comp}" for comp in sorted(available_components)[:20]]
    )

    if len(available_components) > 20:
        error_msg += f"\n  ... and {len(available_components) - 20} more"

    return {"error": error_msg}


def _create_base_node_structure(node_info: dict, component_def: dict) -> dict:
    """Create the base node structure with component definition."""
    node_name = node_info["OriginalType"]

    return {
        "id": node_info["node_id"],
        "type": "WorkflowNode",
        "position": DEFAULT_NODE_DIMENSIONS["position"].copy(),
        "data": {
            "label": component_def.get("display_name", node_name),
            "type": COMPONENT_TYPE_EXCEPTIONS.get(node_name, "component"),
            "originalType": node_name,
            "definition": component_def,
            "config": {},
        },
        "width": DEFAULT_NODE_DIMENSIONS["width"],
        "height": DEFAULT_NODE_DIMENSIONS["height"],
        "selected": False,
        "dragging": False,
        "style": {"opacity": 1},
    }


def _apply_node_configuration(node_structure: dict, node_info: dict) -> None:
    """Apply node-specific configuration from node_info."""
    node_structure["position"] = node_info["position"]
    node_structure["data"]["label"] = node_info["label"]
    node_structure["data"]["config"] = node_info.get("parameters", {})
    node_structure["width"] = node_info["dimension"]["width"]
    node_structure["height"] = node_info["dimension"]["height"]


def _apply_default_input_values(node_structure: dict, node_info: dict) -> None:
    """Apply default values for inputs that have values but aren't in parameters."""
    inputs = node_structure["data"]["definition"]["inputs"]
    parameters = node_info.get("parameters", {})

    for input_item in inputs:
        if input_item["value"] is not None and input_item["name"] not in parameters:
            node_structure["data"]["config"][input_item["name"]] = input_item["value"]


def fulfill_mcp(
    node_info: dict,
    # api_detail_url: str = "https://app-dev.rapidinnovation.dev/api/v1/marketplace/mcps/",
    api_detail_url: str = MCP_URL,
) -> Dict[str, Any]:
    """
    Fetches an MCP from the marketplace by ID and constructs a WorkflowNode.

    Args:
        node_info (dict): Node information containing mcp_id, tool_name, etc.
        api_detail_url (str): Base API URL for MCP marketplace

    Returns:
        dict: Complete MCP node structure
    """
    mcp_id = node_info["mcp_id"]
    tool_name = node_info["tool_name"]

    # Fetch MCP data from API
    mcp_data = _fetch_mcp_data(api_detail_url + mcp_id, mcp_id)

    # Extract tool configuration
    tool_config = _extract_tool_config(mcp_data, tool_name)

    # Build inputs and outputs
    inputs = _build_mcp_inputs(tool_config["props"], tool_config["required"])
    outputs = _build_mcp_outputs(
        tool_config["outputs_schema"], tool_config["has_output"]
    )

    # Create definition and node structure
    definition = _create_mcp_definition(
        mcp_data, mcp_id, tool_name, tool_config["tool"], inputs, outputs, node_info
    )
    node = _create_mcp_node_structure(node_info, definition)

    # Apply configuration and default values
    _apply_mcp_configuration(node, node_info, mcp_data)

    return node


def _fetch_mcp_data(api_url: str, mcp_id: str) -> dict:
    """Fetch MCP data from the marketplace API."""
    response = requests.get(api_url, headers={"Content-Type": "application/json"})
    response.raise_for_status()

    data = response.json()
    mcp = data.get("mcp") or data.get("data") or data

    if not mcp:
        raise ValueError(f"No MCP with id={mcp_id} found in marketplace API")

    return mcp


def _extract_tool_config(mcp_data: dict, tool_name: str) -> dict:
    """Extract tool configuration from MCP data."""
    tools = mcp_data.get("mcp_tools_config", {}).get("tools", [])

    if not tools:
        return {
            "tool": None,
            "props": {},
            "required": set(),
            "has_output": False,
            "outputs_schema": {},
        }

    tool = next((tool for tool in tools if tool["name"] == tool_name), None)
    if not tool:
        return {
            "tool": None,
            "props": {},
            "required": set(),
            "has_output": False,
            "outputs_schema": {},
        }

    props = tool.get("input_schema", {}).get("properties", {})
    required = set(tool.get("input_schema", {}).get("required", []))
    has_output = bool(tool.get("output_schema"))
    outputs_schema = (
        tool.get("output_schema", {}).get("properties", {}) if has_output else {}
    )

    return {
        "tool": tool,
        "props": props,
        "required": required,
        "has_output": has_output,
        "outputs_schema": outputs_schema,
    }


def _build_mcp_inputs(props: dict, required: set) -> list:
    """Build input configuration for MCP node."""
    inputs = []

    for name, schema in props.items():
        input_type = schema.get("type")
        itype = (
            "array"
            if input_type == "array" or "items" in schema
            else input_type or "string"
        )

        inputs.append(
            {
                "name": name,
                "display_name": name[0].upper() + name[1:].replace("_", " "),
                "info": "",
                "input_type": itype,
                "input_types": [itype, "Any"],
                "required": name in required,
                "is_handle": True,
                "is_list": itype == "array",
                "real_time_refresh": False,
                "advanced": False,
                "value": None,
                "options": None,
                "visibility_rules": None,
                "visibility_logic": "OR",
                "validation": {},
            }
        )

    return inputs


def _build_mcp_outputs(outputs_schema: dict, has_output: bool) -> list:
    """Build output configuration for MCP node."""
    outputs = []

    if has_output:
        for name, schema in outputs_schema.items():
            outputs.append(
                {
                    "name": name,
                    "display_name": schema.get("title", name),
                    "output_type": schema.get("type", "Any"),
                }
            )
    else:
        outputs.append(
            {"name": "result", "display_name": "Result", "output_type": "Any"}
        )

    return outputs


def _create_mcp_definition(
    mcp_data: dict,
    mcp_id: str,
    tool_name: str,
    tool: dict,
    inputs: list,
    outputs: list,
    node_info: dict,
) -> dict:
    """Create MCP definition structure."""
    logo = mcp_data.get("logo", "")
    icon = logo.split("/")[-1].split(".")[0].capitalize() if logo else ""

    node = {
        "name": node_info["OriginalType"],
        "display_name": mcp_data.get("name", mcp_id),
        "description": mcp_data.get("description", ""),
        "category": mcp_data.get("category", ""),
        "icon": icon,
        "beta": False,
        "inputs": inputs,
        "outputs": outputs,
        "is_valid": True,
        "type": "MCP",
        "logo": logo,
        "mcp_info": {
            "server_id": mcp_id,
            "server_path": mcp_data.get("hosted_url", ""),
            "tool_name": tool_name,
            "input_schema": tool.get("input_schema", {}) if tool else {},
            "output_schema": tool.get("output_schema", {}) if tool else {},
        },
        "integrations": mcp_data.get("integrations", []),
        "name_slug": mcp_data.get("name_slug", ""),
        "path": "mcp."+node_info["OriginalType"].lower(),
        "platform": mcp_data.get("platform", ""),
        "visible_in_logs_ui": True,
    }
    if not node["mcp_info"]["output_schema"]:
        node["mcp_info"]["output_schema"] = {}
    return node


def _create_mcp_node_structure(node_info: dict, definition: dict) -> dict:
    """Create the base MCP node structure."""
    return {
        "id": node_info["node_id"],
        "type": "WorkflowNode",
        "position": node_info["position"],
        "data": {
            "label": node_info["label"],
            "type": "mcp",
            "originalType": node_info["OriginalType"],
            "definition": definition,
            "config": {},
            "oauthConnectionState": {},
        },
        "width": 388,
        "height": 519,
        "selected": True,
        "positionAbsolute": node_info["position"],
        "dragging": False,
        "style": {"opacity": 1},
    }


def _apply_mcp_configuration(node: dict, node_info: dict, mcp_data: dict) -> None:
    """Apply MCP-specific configuration to the node."""
    # Apply basic configuration
    node["position"] = node_info["position"]
    node["data"]["label"] = node_info["label"]
    node["data"]["config"] = node_info.get("parameters", {})
    node["width"] = node_info["dimension"]["width"]
    node["height"] = node_info["dimension"]["height"]

    # Apply default input values
    inputs = node["data"]["definition"]["inputs"]
    parameters = node_info.get("parameters", {})

    for input_item in inputs:
        if input_item["value"] and input_item["name"] not in parameters:
            node["data"]["config"][input_item["name"]] = input_item["value"]


def fulfill_workflow(
    node_info: dict,
    # api_base_url: str = "https://app-dev.rapidinnovation.dev/api/v1/marketplace/workflows/",
    api_base_url: str = WORKFLOW_URL,
) -> Dict[str, Any]:
    """
    Fetches a workflow from the marketplace and constructs a WorkflowNode.

    Args:
        node_info (dict): Node information containing workflow_id, label, etc.
        api_base_url (str): Base API URL for workflow marketplace

    Returns:
        dict: Complete workflow node structure
    """
    workflow_id = node_info["workflow_id"]

    try:
        # Fetch workflow data from API
        workflow_data = _fetch_workflow_data(api_base_url, workflow_id)

        # Build inputs and outputs
        inputs = _build_workflow_inputs(workflow_data.get("start_nodes", []))
        outputs = _build_workflow_outputs()

        # Create workflow info and definition
        workflow_info = _create_workflow_info(workflow_data, workflow_id)
        definition = _create_workflow_definition(
            workflow_id, node_info["label"], inputs, outputs, workflow_info
        )

        # Create and configure node
        node = _create_workflow_node_structure(node_info, definition)
        _apply_workflow_configuration(node, node_info)

        return node

    except requests.RequestException as e:
        raise Exception(f"Failed to fetch workflow data from marketplace: {str(e)}")
    except json.JSONDecodeError as e:
        raise Exception(f"Failed to parse API response: {str(e)}")
    except Exception as e:
        raise Exception(f"Error generating workflow node: {str(e)}")


def _fetch_workflow_data(api_base_url: str, workflow_id: str) -> dict:
    """Fetch workflow data from the marketplace API."""
    api_url = f"{api_base_url}{workflow_id}"
    headers = {"Content-Type": "application/json"}

    response = requests.get(api_url, headers=headers)

    # Handle specific error cases
    if response.status_code == 404:
        raise Exception(f"Workflow not found in marketplace: {workflow_id}")
    elif response.status_code == 500:
        raise Exception("Internal server error - try again later")
    elif response.status_code != 200:
        raise Exception(f"API returned status code: {response.status_code}")

    response.raise_for_status()
    api_response = response.json()

    if not api_response:
        raise ValueError("Empty response from marketplace API")

    # Extract workflow data from various response formats
    return _extract_workflow_from_response(api_response)


def _extract_workflow_from_response(api_response: dict) -> dict:
    """Extract workflow data from API response with flexible format handling."""
    if not isinstance(api_response, dict):
        raise ValueError("Unexpected response format from marketplace API")

    # Check for common response wrapper formats
    workflow = (
        api_response.get("workflow")
        or api_response.get("data")
        or api_response.get("result")
        or api_response
    )

    if not workflow:
        raise ValueError("No workflow data found in response")

    return workflow


def _build_workflow_inputs(start_nodes: list) -> list:
    """Build input configuration for workflow node."""
    inputs = []

    for start_node in start_nodes:
        is_handle_value = start_node.get("type") == "handle"
        field_name = start_node.get("field", "input_data")

        input_item = {
            "name": field_name,
            "display_name": field_name.replace("_", " ").title(),
            "info": f"Input field: {field_name}",
            "input_type": "string",
            "required": True,
            "is_handle": is_handle_value,
            "is_list": False,
            "real_time_refresh": False,
            "advanced": False,
            "value": None,
            "options": None,
            "visibility_rules": None,
            "visibility_logic": "OR",
            "requirement_rules": None,
            "requirement_logic": "OR",
            "transition_id": start_node.get("transition_id"),
        }
        inputs.append(input_item)

    return inputs


def _build_workflow_outputs() -> list:
    """Build standard workflow outputs."""
    return [
        {
            "name": "execution_status",
            "display_name": "Execution Status",
            "output_type": "string",
        },
        {
            "name": "workflow_execution_id",
            "display_name": "Execution ID",
            "output_type": "string",
        },
        {"name": "message", "display_name": "Message", "output_type": "string"},
    ]


def _create_workflow_info(workflow_data: dict, workflow_id: str) -> dict:
    """Create workflow info structure."""
    return {
        "id": workflow_id,
        "name": workflow_data.get("name", ""),
        "description": workflow_data.get("description", ""),
        "workflow_url": workflow_data.get("workflow_url"),
        "builder_url": workflow_data.get("builder_url"),
        "start_nodes": workflow_data.get("start_nodes", []),
        "owner_id": workflow_data.get("owner_id"),
        "user_ids": (
            [workflow_data.get("owner_id")] if workflow_data.get("owner_id") else []
        ),
        "owner_type": "user",
        "workflow_template_id": None,
        "template_owner_id": None,
        "is_imported": False,
        "version": workflow_data.get("version", "1.0.0"),
        "visibility": workflow_data.get("visibility", "private").lower(),
        "category": workflow_data.get("category"),
        "tags": workflow_data.get("tags"),
        "status": workflow_data.get("status", "active"),
        "is_changes_marketplace": False,
        "is_customizable": True,
        "auto_version_on_update": False,
        "created_at": workflow_data.get("created_at"),
        "updated_at": workflow_data.get("updated_at"),
        "available_nodes": workflow_data.get("available_nodes") or [],
        "is_updated": True,
        "source_version_id": workflow_data.get("source_version_id"),
    }


def _create_workflow_definition(
    workflow_id: str, label: str, inputs: list, outputs: list, workflow_info: dict
) -> dict:
    """Create workflow definition structure."""
    return {
        "name": f"workflow-{workflow_id}",
        "display_name": label,
        "description": label,
        "category": "Workflows",
        "icon": "Workflow",
        "beta": False,
        "path": f"workflow.{workflow_id}",
        "inputs": inputs,
        "outputs": outputs,
        "is_valid": True,
        "type": "Workflow",
        "workflow_info": workflow_info,
    }


def _create_workflow_node_structure(node_info: dict, definition: dict) -> dict:
    """Create the base workflow node structure."""
    return {
        "id": node_info["node_id"],
        "type": "WorkflowNode",
        "position": node_info["position"],
        "data": {
            "label": node_info["label"],
            "type": "component",
            "originalType": f"workflow-{node_info['workflow_id']}",
            "definition": definition,
            "config": node_info.get("parameters", {}),
        },
        "width": 388,
        "height": 326,
        "selected": False,
        "dragging": False,
        "style": {"opacity": 1},
    }


def _apply_workflow_configuration(node: dict, node_info: dict) -> None:
    """Apply workflow-specific configuration to the node."""
    node["position"] = node_info["position"]
    node["data"]["label"] = node_info["label"]
    node["data"]["config"] = node_info.get("parameters", {})
    node["width"] = node_info["dimension"]["width"]
    node["height"] = node_info["dimension"]["height"]


def _return_node_template(node_info: dict) -> dict:
    """
    Return the appropriate node template based on node type.

    Args:
        node_info (dict): Node information containing type and other details

    Returns:
        dict: Complete node structure based on type

    Raises:
        ValueError: If node type is unknown
    """
    node_type = node_info["type"].lower().strip()

    node_type_handlers = {
        "component": fulfill_component,
        "workflow": fulfill_workflow,
        "mcp": fulfill_mcp,
    }

    handler = node_type_handlers.get(node_type)
    if not handler:
        raise ValueError(f"Unknown node type: {node_type}")

    return handler(node_info=node_info)


def _return_edge_template(edge: dict) -> dict:
    """
    Create edge template with proper formatting.

    Args:
        edge (dict): Edge information containing source, target, handles

    Returns:
        dict: Complete edge structure for React Flow
    """
    template = {
        "animated": True,
        "style": {"strokeWidth": 2, "zIndex": 5},
        "type": "default",
        "selected": False,
    }

    template.update(edge)
    template["id"] = _generate_edge_id(edge)

    return template


def _generate_edge_id(edge: dict) -> str:
    """Generate unique edge ID from edge properties."""
    return (
        f"reactflow__edge{edge['source']}{edge['sourceHandle']}-"
        f"{edge['target']}{edge['targetHandle']}"
    )

def _find_component(nodes, edges, start_node):
    """
    Find all nodes in the connected component that contains the start_node.

    Args:
        nodes: Set of node IDs in the graph
        edges: List of edge dictionaries with 'source' and 'target' keys
        start_node: The node ID to start the search from

    Returns:
        Set of node IDs that are in the same connected component as start_node
    """
    if start_node not in nodes:
        return set()

    # Build adjacency list for undirected graph
    adjacency = {node: set() for node in nodes}
    for edge in edges:
        source = edge.get('source')
        target = edge.get('target')
        if source in nodes and target in nodes:
            adjacency[source].add(target)
            adjacency[target].add(source)

    # Perform BFS/DFS to find connected component
    visited = set()
    queue = [start_node]
    visited.add(start_node)

    while queue:
        current = queue.pop(0)
        for neighbor in adjacency[current]:
            if neighbor not in visited:
                visited.add(neighbor)
                queue.append(neighbor)

    return visited

def _fix_for_validation(workflow: dict) -> dict:
    node_id = set()
    handle_id = set()
    for node in workflow["nodes"]:
        if node["id"] in node_id:
            continue
        node_id.add(node["id"])
        for input in node["data"]["definition"]["inputs"]:
            handle_id.add(f"{node['id']}_{input['name']}")
        for output in node["data"]["definition"]["outputs"]:
            handle_id.add(f"{node['id']}_{output['name']}")
    edges = []
    for edge in workflow["edges"]:
        if f"{edge['target']}_{edge['targetHandle']}" not in handle_id:
            continue
        if f"{edge['source']}_{edge['sourceHandle']}" not in handle_id:
            continue
        edges.append(edge)
    node_id = _find_component(node_id, edges, "start-node")
    nodes = [node for node in workflow["nodes"] if node["id"] in node_id]
    return {"nodes": nodes, "edges": edges}

def post_processing(workflow: dict) -> dict:
    """
    Post-process workflow data to create complete node and edge structures.

    Args:
        workflow (dict): Raw workflow data with nodes and edges

    Returns:
        dict: Processed workflow with complete node and edge structures
    """
    output = {"nodes": [], "edges": []}
    required_parameters = []

    # Process nodes
    for node in workflow["nodes"]:
        if node["OriginalType"] == "StartNode":
            node["node_id"] = "start-node"

        processed_node = _return_node_template(node)
        output["nodes"].append(processed_node)

    # Process edges
    for edge in workflow["edges"]:
        target = edge["target"]
        target_handle = edge["targetHandle"]

        # Remove satisfied requirements
        if (target, target_handle) in required_parameters:
            required_parameters.remove((target, target_handle))

        output["edges"].append(_return_edge_template(edge))
    output = _fix_for_validation(output)
    # Add missing edges for required parameters
    _add_missing_edges(required_parameters, output)

    nodes = output["nodes"]
    rectangles = []
    for node in nodes:
        inputs = node["data"]["definition"]["inputs"]
        inputs = sum([1 for input in inputs if input["is_handle"]])
        outputs = len(node["data"]["definition"]["outputs"])
        width, height = calculate_node_size(inputs, outputs)
        width = width * 388 / 208
        height = height * 388 / 208
        node["width"] = int(width)
        node["height"] = int(height)
        rectangles.append(
            {
                "id": node["id"],
                "x": node["position"]["x"],
                "y": node["position"]["y"],
                "w": node["width"],
                "h": node["height"],
            }
        )
    separation = 100.0
    new_layout = rectangle_layout_with_separation(
        rectangles, separation=separation, fixed_node_id="start-node"
    )
    new_layout = {
        rect["id"]: {"x": int(rect["x"]), "y": int(rect["y"])} for rect in new_layout
    }
    for node in output["nodes"]:
        node["position"] = new_layout[node["id"]]
        node["positionAbsolute"] = new_layout[node["id"]]
    return output


def _track_required_parameters(node: dict, required_parameters: list) -> None:
    """Track required parameters that need connections."""
    inputs = node["data"]["definition"]["inputs"]
    config = node["data"]["config"]

    for input_item in inputs:
        if (
            input_item["required"]
            and input_item["name"] not in config
            and input_item["is_handle"]
        ):
            required_parameters.append((node["id"], input_item["name"]))


def _add_missing_edges(required_parameters: list, output: dict) -> None:
    """Add edges for required parameters that don't have connections."""
    for target, target_handle in required_parameters:
        edge = {
            "source": "start-node",
            "sourceHandle": "flow",
            "target": target,
            "targetHandle": target_handle,
        }
        output["edges"].append(_return_edge_template(edge))
