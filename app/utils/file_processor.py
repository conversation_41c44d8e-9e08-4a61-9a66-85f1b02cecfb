import aiohttp
import asyncio
from typing import List, Dict, Tuple, Any
from urllib.parse import urlparse
import logging
import os

supported_image_types = ["png", "jpeg", "jpg"]
supported_document_types = [
    "pdf",
    "csv",
    "docx",
    "txt",
    "md",
]

logging.basicConfig(level=logging.ERROR)


def get_file_extension(url: str):
    path = urlparse(url).path
    ext = os.path.splitext(path)[1].lstrip(".").lower()

    if ext in supported_image_types:
        return {"type": "image", "extension": ext}
    elif ext in supported_document_types:
        return {"type": "document", "extension": ext}
    else:
        logging.error(f"Unsupported file extension: {ext}")
        return {"type": None, "extension": None}


async def fetch_gcs_file_bytes(url: str) -> bytes:
    """Fetch bytes from a GCS URL."""
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as response:
            response.raise_for_status()
            return await response.read()


async def fetch_multiple_gcs_files(
    attachments: List[Dict[str, Any]],
) -> List[Tuple[bytes, Dict[str, Any]]]:
    """Fetch bytes and file info from multiple attachments concurrently."""
    if not attachments:
        return []
    tasks = [fetch_gcs_file_bytes(att["file_url"]) for att in attachments]
    bytes_list = await asyncio.gather(*tasks)
    file_info_list = [get_file_extension(att["file_url"]) for att in attachments]
    return list(zip(bytes_list, file_info_list))


def extract_text_from_document(
    file_bytes: bytes, file_extension: str, file_name: str
) -> str:
    """
    Extract text content from various document formats.

    Args:
        file_bytes: Raw file bytes
        file_extension: File extension (txt, pdf, docx, etc.)
        file_name: Original file name for reference

    Returns:
        Extracted text content or error message
    """
    try:
        if file_extension in ["txt", "md"]:
            return _extract_text_from_plain_text(file_bytes)
        elif file_extension == "csv":
            return _extract_text_from_csv(file_bytes, file_name)
        elif file_extension == "docx":
            return _extract_text_from_docx(file_bytes)
        elif file_extension == "pdf":
            return _extract_text_from_pdf(file_bytes)
        else:
            return f"Unsupported document format: {file_extension}"
    except Exception as e:
        logging.error(f"Failed to extract text from {file_name}: {e}")
        return f"Error extracting text from {file_name}: {str(e)}"


def _extract_text_from_plain_text(file_bytes: bytes) -> str:
    """Extract text from plain text files (txt, md)."""
    return file_bytes.decode("utf-8")


def _extract_text_from_csv(file_bytes: bytes, file_name: str) -> str:
    """Extract and format text from CSV files."""
    import csv
    import io

    content_str = file_bytes.decode("utf-8")
    csv_reader = csv.reader(io.StringIO(content_str))
    rows = list(csv_reader)

    if not rows:
        return "Empty CSV file"

    headers = rows[0]
    data_rows = rows[1:10]  # First 10 rows to avoid too much content

    csv_text = f"CSV File: {file_name}\n"
    csv_text += f"Headers: {', '.join(headers)}\n\n"

    for idx, row in enumerate(data_rows, 1):
        csv_text += f"Row {idx}: {', '.join(row)}\n"

    if len(rows) > 11:
        csv_text += f"\n... and {len(rows) - 11} more rows"

    return csv_text


def _extract_text_from_docx(file_bytes: bytes) -> str:
    """Extract text from DOCX files."""
    import docx2txt
    import tempfile

    with tempfile.NamedTemporaryFile(suffix=".docx", delete=False) as tmp_file:
        tmp_file.write(file_bytes)
        tmp_file.flush()

        try:
            doc_text = docx2txt.process(tmp_file.name)
            return (
                doc_text.strip() if doc_text else "No text content found in DOCX file"
            )
        finally:
            import os

            os.unlink(tmp_file.name)


def _extract_text_from_pdf(file_bytes: bytes) -> str:
    """Extract text from PDF files."""
    import pypdf
    import io

    pdf_reader = pypdf.PdfReader(io.BytesIO(file_bytes))

    pdf_text = ""
    for page in pdf_reader.pages:
        pdf_text += page.extract_text() + "\n"

    return pdf_text.strip() if pdf_text.strip() else "No text content found in PDF file"


def format_document_content(text_content: str, file_name: str) -> str:
    """Format extracted text content with file boundaries."""
    return f"\n\n--- Content of {file_name} ---\n{text_content}\n--- End of {file_name} ---\n"


async def process_attachments_for_agent(attachments: list[dict]) -> list[dict]:
    """
    Process attachments and return formatted content for agent consumption.

    Args:
        attachments: List of attachment dictionaries with file_url and file_name

    Returns:
        List of content dictionaries ready for agent processing
    """
    if not attachments:
        return []

    content_list = []
    file_data_list = await fetch_multiple_gcs_files(attachments)

    for i, (file_bytes, file_info) in enumerate(file_data_list):
        file_name = attachments[i].get("file_name", f"file.{file_info['extension']}")

        if file_info["type"] == "image":
            # Images are processed as binary attachments
            content_list.append(
                {
                    "image": {
                        "format": file_info["extension"],
                        "source": {"bytes": file_bytes},
                    }
                }
            )
        elif file_info["type"] == "document":
            # Documents are converted to text content
            extracted_text = extract_text_from_document(
                file_bytes, file_info["extension"], file_name
            )

            if extracted_text:
                formatted_text = format_document_content(extracted_text, file_name)
                content_list.append({"text": formatted_text})
            else:
                # Fallback for unsupported formats
                logging.warning(f"Could not extract text from {file_name}, skipping")
        else:
            logging.warning(f"Unknown file type for {file_name}")

    return content_list
