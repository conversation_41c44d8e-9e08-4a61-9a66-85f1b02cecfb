"""
Mem0 + Qdrant + LangG<PERSON>h Integration for Long Term Memory
-----------------------------------------------------
Implements background memory storage with asyncio so that the
tool immediately returns "Memory sent to store" while storage runs in background.
"""

import asyncio
import logging
import os
from typing import Optional, Dict, Any

from dotenv import load_dotenv
from mem0 import Memory
from langchain_core.tools import tool

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global singleton for Memory instance
_memory_instance: Optional[Memory] = None


def setup_mem0_with_qdrant() -> Memory:
    """
    Initialize mem0 with Qdrant instance.
    This should be called once during application startup.
    """

    qdrant_host = os.getenv("QDRANT_HOST", "localhost")
    qdrant_port = int(os.getenv("QDRANT_PORT", "6333"))
    qdrant_api_key = os.getenv("QDRANT_API_KEY")
    qdrant_collection_name = os.getenv(
        "AGENT_MEMORY_COLLECTION_NAME", "langgraph_memory"
    )
    qdrant_vector_size = int(os.getenv("QDRANT_VECTOR_SIZE", "1536"))

    # Configure Mem0 with Qdrant
    qdrant_config = {
        "collection_name": qdrant_collection_name,
        "embedding_model_dims": qdrant_vector_size,
        "api_key": qdrant_api_key,
        "url": f"https://{qdrant_host}:{qdrant_port}",
    }

    config = {
        "vector_store": {"provider": "qdrant", "config": qdrant_config},
        "llm": {
            "provider": "openai",
            "config": {
                "model": "gpt-5-mini",
                "temperature": 0.2,
                "max_tokens": 1500,
            },
        },
        "embedder": {
            "provider": "openai",
            "config": {"model": "text-embedding-3-small"},
        },
    }

    return Memory.from_config(config)


def get_memory_instance() -> Memory:
    """
    Get or initialize the global memory instance.
    """
    global _memory_instance
    if _memory_instance is None:
        _memory_instance = setup_mem0_with_qdrant()
    return _memory_instance


def initialize_memory():
    """
    Initialize the memory instance during startup.
    """
    global _memory_instance
    if _memory_instance is None:
        _memory_instance = setup_mem0_with_qdrant()


async def _store_memory_impl_async(
    text: str,
    user_id: str,
    conversation_id: Optional[str] = None,
    agent_id: Optional[str] = None,
    additional_metadata: Optional[Dict[str, Any]] = None,
):
    """
    Async implementation of memory storage.
    Runs in background via asyncio.create_task().
    """
    try:
        memory = get_memory_instance()

        metadata = {"user_id": user_id}
        if conversation_id:
            metadata["conversation_id"] = conversation_id
        if agent_id:
            metadata["agent_id"] = agent_id
        if additional_metadata:
            metadata.update(additional_metadata)

        loop = asyncio.get_running_loop()
        # Run blocking memory.add() in thread executor to avoid blocking event loop
        result = await loop.run_in_executor(
            None, lambda: memory.add(messages=text, user_id=user_id, metadata=metadata)
        )

        logger.info(f"✅ Memory stored successfully for user {user_id}. IDs: {result}")

    except Exception as e:
        logger.exception(f"❌ Error storing memory for user {user_id}: {str(e)}")


def _retrieve_memories_impl(
    query: str,
    user_id: str,
    conversation_id: Optional[str] = None,
    agent_id: Optional[str] = None,
    limit: int = 5,
):
    """
    Retrieve stored memories synchronously.
    """
    try:
        memory = get_memory_instance()

        filters = {"user_id": user_id}
        if conversation_id:
            filters["conversation_id"] = conversation_id
        if agent_id:
            filters["agent_id"] = agent_id

        results = memory.search(
            query=query, user_id=user_id, limit=limit, filters=filters
        )
        return results

    except Exception as e:
        logger.exception("Error retrieving memories")
        return [{"error": f"Error retrieving memories: {str(e)}"}]


def get_store_memory_tool(
    user_id: str,
    conversation_id: Optional[str] = None,
    agent_id: Optional[str] = None,
):
    """
    Create a LangGraph tool that stores memory asynchronously in the background.
    """

    @tool
    async def store_memory(
        text: str,
        additional_metadata: Optional[Dict[str, Any]] = None,
    ) -> str:
        """
        Store information in memory for the current user and conversation.

        Returns immediately with 'Memory sent to store'.
        """
        try:
            asyncio.create_task(
                _store_memory_impl_async(
                    text=text,
                    user_id=user_id,
                    conversation_id=conversation_id,
                    agent_id=agent_id,
                    additional_metadata=additional_metadata,
                )
            )
            return "Memory sent to store ✅"

        except Exception as e:
            logger.exception("Failed to create background memory task")
            return f"Error scheduling memory storage: {str(e)}"

    return store_memory


def get_retrieve_memories_tool(
    user_id: str,
    conversation_id: Optional[str] = None,
    agent_id: Optional[str] = None,
):
    """
    Create a LangGraph tool that retrieves stored memories.
    """

    @tool
    def retrieve_memories(query: str, limit: int = 5):
        """
        Retrieve relevant memories for the current user and conversation.
        """
        return _retrieve_memories_impl(
            query=query,
            user_id=user_id,
            conversation_id=conversation_id,
            agent_id=agent_id,
            limit=limit,
        )

    return retrieve_memories
