"""
Web Search tool , powered by Exa's API. This tool returns structured search results with title, URL and content.
"""

import json
import os
import logging
from typing import Any, Dict

import aiohttp
from dotenv import load_dotenv
from langchain_core.tools import tool

load_dotenv()

logger = logging.getLogger(__name__)

EXA_API_BASE_URL = os.getenv("EXA_API_BASE_URL")
EXA_SEARCH_ENDPOINT = os.getenv("EXA_SEARCH_ENDPOINT")
EXA_API_KEY = os.getenv("EXA_API_KEY")


def _format_search_response(data: Dict[str, Any], query: str) -> str:
    """
    Format the search response to structured output with only essential fields.

    Args:
        data: Raw response data from Exa API
        query: Original search query

    Returns:
        JSON string with structured format containing query and results
    """
    if "results" in data:
        structured_response = {"query": query, "results": []}

        for result in data["results"]:
            formatted_result = {
                "id": result.get("id", ""),
                "title": result.get("title", ""),
                "url": result.get("url", ""),
                "publishedDate": result.get("publishedDate", ""),
            }

            if "text" in result:
                formatted_result["text"] = result["text"]

            if "summary" in result:
                formatted_result["summary"] = result["summary"]

            structured_response["results"].append(formatted_result)

        return json.dumps(structured_response, indent=2)
    else:
        return json.dumps(data, indent=2)


@tool()
async def web_search_tool(query: str) -> str:
    """
    Search the web using Exa's API.

    Args:
        query: The search query string

    Returns:
        JSON string containing search results with title, URL, content, and metadata.
    """
    try:
        if not query or not query.strip():
            raise ValueError("Query parameter is required and cannot be empty")

        api_key = EXA_API_KEY

        payload = {
            "query": query,
            "numResults": 5,
            "contents": {"summary": {"query": query}}
        }

        headers = {"x-api-key": api_key, "Content-Type": "application/json"}
        url = f"{EXA_API_BASE_URL}{EXA_SEARCH_ENDPOINT}"

        logger.info(f"Making Exa search request for query: {query}")

        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=payload, headers=headers) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(
                        f"API request failed with status {response.status}: {error_text}"
                    )

                try:
                    data = await response.json()
                except Exception as e:
                    raise Exception(f"Failed to parse API response: {str(e)}")

        formatted_response = _format_search_response(data, query)
        return formatted_response

    except aiohttp.ClientError as e:
        raise Exception(f"Connection error: {str(e)}")
    except ValueError as e:
        raise ValueError(str(e))
    except Exception as e:
        logger.error(f"Unexpected error in web_search: {str(e)}")
        raise Exception(f"Unexpected error: {str(e)}")
