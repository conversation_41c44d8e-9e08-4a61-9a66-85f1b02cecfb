import asyncio
from typing import Annotated
from langchain_core.tools import tool
from langgraph.config import get_stream_writer
from langgraph.prebuilt import InjectedState


@tool()
async def workflow_execution(
    recipient: str,
    message: str,
    workflow_name: str,
    workflow_id: str,
    state: Annotated[dict, InjectedState] | None = None,
) -> str:
    """Simulate a workflow of sending an email via SMS."""
    writer = get_stream_writer()

    # Get tool_call_id from state
    tool_call_id = ""
    if state and "messages" in state:
        last_message = state["messages"][-1]
        if hasattr(last_message, "tool_calls") and last_message.tool_calls:
            for tool_call in last_message.tool_calls:
                if tool_call.get("name") == "workflow_execution":
                    tool_call_id = tool_call.get("id", "")
                    break

    # Send workflow start event
    writer(
        {
            "type": "workflow_start",
            "workflow_name": workflow_name,
            "workflow_id": workflow_id,
            "tool_id": tool_call_id,
        }
    )

    # Define workflow steps as JSON objects
    workflow_steps = [
        {
            "step_name": "gateway_init",
            "status": "initializing",
            "message": "📡 Initializing SMS gateway connection...",
            "timestamp": asyncio.get_event_loop().time(),
        },
        {
            "step_name": "auth",
            "status": "authenticating",
            "message": "🔐 Authenticating sender credentials...",
            "credentials_valid": True,
            "timestamp": asyncio.get_event_loop().time(),
        },
        {
            "step_name": "content_prep",
            "status": "preparing",
            "message": "✉️ Preparing email content for SMS format...",
            "content_length": len(message),
            "recipient": recipient,
            "timestamp": asyncio.get_event_loop().time(),
        },
        {
            "step_name": "send_sms",
            "status": "sending",
            "message": "📤 Sending SMS to gateway...",
            "gateway": "twilio",
            "timestamp": asyncio.get_event_loop().time(),
        },
        {
            "step_name": "delivery_wait",
            "status": "waiting",
            "message": "📶 Waiting for delivery confirmation...",
            "timeout": 30,
            "timestamp": asyncio.get_event_loop().time(),
        },
        {
            "step_name": "delivery_confirm",
            "status": "delivered",
            "message": "✅ SMS successfully delivered to recipient.",
            "delivery_time": "2.3s",
            "timestamp": asyncio.get_event_loop().time(),
        },
        {
            "step_name": "completion",
            "status": "completed",
            "message": "📩 Email delivery simulation completed successfully.",
            "total_duration": "45.2s",
            "timestamp": asyncio.get_event_loop().time(),
        },
    ]

    # List to accumulate all completed steps
    completed_steps = []

    for i, step in enumerate(workflow_steps):
        # Fixed 2 second delay between steps
        delay = 2.0
        await asyncio.sleep(delay)

        # Update timestamp in step
        step["timestamp"] = asyncio.get_event_loop().time()
        step["delay_seconds"] = delay

        # Add to completed steps
        completed_steps.append(step)

        # Stream each step
        writer(
            {
                "type": "workflow_step",
                "workflow_name": workflow_name,
                "workflow_id": workflow_id,
                "step_data": step,
                "tool_id": tool_call_id,
            }
        )

    # Send completion event
    writer(
        {
            "type": "workflow_end",
            "workflow_name": workflow_name,
            "workflow_id": workflow_id,
            "workflow_steps": completed_steps,
            "db_save": True,
        }
    )

    await asyncio.sleep(0.05)

    # Return summary
    return f"Workflow '{workflow_name}' (ID: {workflow_id}) completed successfully. All {len(workflow_steps)} steps executed."
