import os
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from pydantic import SecretStr

load_dotenv()


def get_chat_model(provider: str = "", model_name: str = "") -> ChatOpenAI:
    """
    Safely create a ChatOpenAI model instance.
    Falls back to defaults if input or environment values are invalid.

    Args:
        provider (str, optional): Provider name (e.g., 'openai', 'anthropic'). Defaults to env var DEFAULT_PROVIDER.
        model_name (str, optional): Model name (e.g., 'gpt-4o-mini'). Defaults to env var DEFAULT_MODEL_NAME.

    Returns:
        ChatOpenAI: Configured chat model instance.
    """
    try:
        # Read defaults
        default_provider = os.getenv("DEFAULT_PROVIDER", "openai")
        default_model_name = os.getenv("DEFAULT_MODEL_NAME", "gpt-4o-mini")
        default_base_url = os.getenv(
            "OPENROUTER_BASE_URL", "https://openrouter.ai/api/v1"
        )

        # Use provided values or fall back to defaults
        provider = provider or default_provider
        model_name = model_name or default_model_name
        api_key = os.getenv("OPENROUTER_API_KEY")  # always from OPENROUTER
        base_url = os.getenv("OPENROUTER_BASE_URL", default_base_url)

        if not api_key:
            raise ValueError("Missing OPENROUTER_API_KEY")

        # Build model string
        model_str = f"{provider}/{model_name}"

        return ChatOpenAI(
            api_key=SecretStr(api_key),
            base_url=base_url,
            model=model_str,
        )

    except Exception as e:
        print(f"[WARN] Falling back to defaults due to error: {e}")

        # Safe fallback
        fallback_provider = os.getenv("DEFAULT_PROVIDER", "openai")
        fallback_model_name = os.getenv("DEFAULT_MODEL_NAME", "gpt-4o-mini")
        fallback_api_key = os.getenv("OPENROUTER_API_KEY")
        fallback_base_url = os.getenv(
            "OPENROUTER_BASE_URL", "https://openrouter.ai/api/v1"
        )

        return ChatOpenAI(
            api_key=SecretStr(fallback_api_key) if fallback_api_key else None,
            base_url=fallback_base_url,
            model=f"{fallback_provider}/{fallback_model_name}",
        )
