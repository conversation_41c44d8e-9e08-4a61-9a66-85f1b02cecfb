"""State management for global agent with TODO tracking

This module defines the extended agent state structure that supports
- Task planning and progress tracking through TODO lists
"""

from typing import Literal, NotRequired
from typing_extensions import TypedDict

from langgraph.prebuilt.chat_agent_executor import AgentState


class Todo(TypedDict):
    """A structured task item for tracking progress through complex workflows.

    Attributes:
        content: Short, specific description of the task
        status: Current state - pending, in_progress, or completed
    """

    content: str
    status: Literal["pending", "in_progress", "completed"]


class GlobalAgentState(AgentState):
    """Extended agent state that includes task tracking

    Inherits from LangGraph's AgentState and adds:
    - todos: List of Todo items for task planning and progress tracking
    """

    todos: NotRequired[list[Todo]]
