from typing import Optional
from langgraph.prebuilt import create_react_agent
from langgraph_supervisor import create_supervisor


from app.agent_1.model import get_chat_model
from app.agent_1.prompts import (
    TODO_USAGE_INSTRUCTIONS,
)
from app.agent_1.tools import memory_tools
from app.agent_1.tools.todo import write_todos, read_todos
from app.agent_1.tools.knowledge_base import knowledge_base
from app.agent_1.tools.web_search_tool import web_search_tool
from app.agent_1.tools.workflow_execution import workflow_execution
from app.services.stream_formatter import final_formatter
from dotenv import load_dotenv

load_dotenv()


INSTRUCTIONS = (
    "# TODO MANAGEMENT\n"
    + TODO_USAGE_INSTRUCTIONS
    + "\n\n"
    + "=" * 80
    + "\n\n"
    + "# SUB-AGENT DELEGATION\n"
)


def _create_supervisor(
    provider: str,
    model_name: str,
    use_knowledge_base: bool,
    use_search: bool,
    user_id: str,
    conversation_id: str,
    agent_id: Optional[str] = None,
    use_memory_cross_conversations: bool = True,
):
    model = get_chat_model(provider=provider, model_name=model_name)

    if hasattr(model, "bind"):
        model = model.bind(strict=True)

    agents = []

    if use_knowledge_base:
        knowledge_base_subagent = create_react_agent(
            model=model,
            tools=[knowledge_base],
            prompt=(
                "You are a knowledge base agent, whose job is to get info from the knowledge base, use the knowledge base tool to get response "
            ),
            name="knowledge_base_agent",
        )
        agents.append(knowledge_base_subagent)

    if use_search:
        web_search_subagent = create_react_agent(
            model=model,
            tools=[web_search_tool],
            prompt=(
                "You are a web search agent, if a query comes for web search use the web search tool to get response "
            ),
            name="web_search_agent",
        )
        agents.append(web_search_subagent)

    add_memory_tool = memory_tools.get_store_memory_tool(
        user_id=user_id, conversation_id=conversation_id, agent_id=agent_id
    )

    if use_memory_cross_conversations:
        get_memory_tool = memory_tools.get_retrieve_memories_tool(
            user_id=user_id, agent_id=agent_id
        )

    else:
        get_memory_tool = memory_tools.get_retrieve_memories_tool(
            user_id=user_id, conversation_id=conversation_id
        )

    supervisor = create_supervisor(
        model=model,
        supervisor_name="global_agent",
        agents=agents,
        tools=[
            write_todos,
            read_todos,
            workflow_execution,
            get_memory_tool,
            add_memory_tool,
        ],
        prompt=(INSTRUCTIONS),
        add_handoff_back_messages=True,
        output_mode="full_history",
    ).compile()

    return supervisor


async def run_global_stream(
    user_message: str,
    provider: str,
    model: str,
    use_knowledge_base: bool,
    use_search: bool,
    user_id: str,
    conversation_id: str,
    agent_id: Optional[str] = None,
    use_memory_cross_conversations: bool = True,
):
    supervisor = _create_supervisor(
        provider,
        model,
        use_knowledge_base,
        use_search,
        user_id,
        conversation_id,
        agent_id,
        use_memory_cross_conversations,
    )

    stream_start_chunk = {"type": "stream_start"}
    yield (stream_start_chunk)
    async for chunk in supervisor.astream_events(
        {
            "messages": [
                {
                    "role": "user",
                    "content": user_message,
                }
            ]
        },
        stream_mode=["messages", "updates", "values", "custom"],
        subgraphs=True,
    ):
        formatted_chunk = final_formatter(chunk)
        if formatted_chunk:
            yield formatted_chunk

    stream_end_chunk = {"type": "stream_end"}
    yield (stream_end_chunk)


async def run_global(
    user_message: str,
    provider: str,
    model: str,
    use_knowledge_base: bool,
    use_search: bool,
    user_id: str,
    conversation_id: str,
    agent_id: Optional[str] = None,
    use_memory_cross_conversations: bool = True,
):
    supervisor = _create_supervisor(
        provider,
        model,
        use_knowledge_base,
        use_search,
        user_id,
        conversation_id,
        agent_id,
        use_memory_cross_conversations,
    )

    result = None
    async for chunk in supervisor.astream_events(
        {
            "messages": [
                {
                    "role": "user",
                    "content": user_message,
                }
            ]
        },
        stream_mode="values",
    ):
        result = chunk

    return result
