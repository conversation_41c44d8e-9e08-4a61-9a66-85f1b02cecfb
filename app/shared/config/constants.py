from enum import Enum


class RedisStreamEnum(str, Enum):
    """Redis Streams names for agent communication"""

    AGENT_REQUESTS = "{env}:agent:chat:requests"
    AGENT_RESPONSES = "{env}:agent:chat:responses:{conversation_id}"


class RedisConsumerGroupEnum(str, Enum):
    """Redis Streams consumer group names"""

    AGENT_PROCESSORS = "agent-processors"


class RequestPlatforms(str, Enum):
    """Platforms for which the agent can be used"""

    WEB = "web"
    SMS = "sms"
    GLOBAL = "global"


class SSEEventType(str, Enum):
    STREAM_START = "stream_start"
    MESSAGE_START = "message_start"
    CHUNK = "chunk"
    TOOL_USE_START = "tool_use_start"
    TOOL_CHUNK = "tool_chunk"
    TOOL_RESULT = "tool_result"
    TEXT_RESULT = "text_result"
    STREAM_END = "stream_end"


class RedisWorkflowStreamEnum(str, Enum):
    """Redis Streams names for workflow communication"""

    WORKFLOW_REQUESTS = "{env}:workflow:requests"
    WORKFLOW_RESPONSES = "{env}:workflow:responses"

    WORKFLOW_CHAT_RESPONSES = "{env}:workflow:chat:responses"
    WORKFLOW_CHAT_REQUESTS = "{env}:workflow:chat:requests"


class RedisWorkflowConsumerGroupEnum(str, Enum):
    """Redis Streams consumer group names"""

    WORKFLOW_PROCESSORS = "workflow-processors"


class Workflow_SSEEventType(str, Enum):
    """Server Sent Event types for workflow generation"""

    STREAM_START = "stream_start"
    SUB_AGENT_CALLED = "sub_agent_called"
    MESSAGE = "message"
    TOOL_CALLED = "tool_called"
    PRE_PROCESSING = "pre_processing"
    POST_PROCESSING = "post_processing"
    WORKFLOW_GENERATED = "workflow_generated"
    ERROR = "error"
    STREAM_END = "stream_end"


class EventType(str, Enum):
    TOOL_STREAM = "tool_stream"
    MESSAGE_START = "message_start"
    MESSAGE = "message"
    MESSAGE_END = "message_end"
    TOOL_START = "tool_start"
    TOOL_END = "tool_end"
