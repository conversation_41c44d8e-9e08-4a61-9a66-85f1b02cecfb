import logging
import time
import uuid

import aiohttp

from app.agent.global_agent import run_agent, run_agent_stream
from app.agent_1.ruh_global_agent import run_global_stream
from app.services.model_config import DEFAULT_MODEL_NAME, DEFAULT_PROVIDER
from app.services.redis_streams import RedisStreamsManager
from app.shared.config.base import get_settings
from app.shared.config.constants import (
    RedisConsumerGroupEnum,
    RedisStreamEnum,
    RedisWorkflowStreamEnum,
    RequestPlatforms,
)
from app.workflow_chat.agent import workflow_chat
from app.workflow_generation_graph.graph import generate_workflow

settings = get_settings()


async def listen_event_from_redis_streams():
    """Listen for events from Redis Streams using consumer groups"""
    logging.info(f"Listening for events from Redis Streams: {int(time.time())}")

    # Initialize Redis Streams manager
    streams_manager = RedisStreamsManager()

    # Create a unique consumer name for this instance
    consumer_name = f"agent-worker-{uuid.uuid4().hex[:8]}"

    # Get consumer for agent requests
    consumer = streams_manager.get_consumer(
        group=RedisConsumerGroupEnum.AGENT_PROCESSORS.value, consumer_name=consumer_name
    )

    logging.info(
        f"Starting consumer {consumer_name} for group {RedisConsumerGroupEnum.AGENT_PROCESSORS.value}"
    )

    try:
        # Handle any pending messages first
        await streams_manager.handle_pending_messages(
            group=RedisConsumerGroupEnum.AGENT_PROCESSORS.value,
            consumer_name=consumer_name,
            stream=RedisStreamEnum.AGENT_REQUESTS.value.format(
                env=settings.environment
            ),
        )

        # Consume agent request messages
        async for message in consumer.consume_agent_requests(
            count=settings.redis.consumer_batch_size
        ):
            logging.info(
                f"Received message from stream {message.stream}: ID={message.id}, timestamp={int(time.time())}"
            )
            processing_successful = False
            retry_needed = True
            if message.stream == RedisStreamEnum.AGENT_REQUESTS.value.format(
                env=settings.environment
            ):
                while retry_needed:
                    try:
                        # Extract message data
                        conversation_id = message.fields.get("conversation_id")
                        request_id = message.fields.get("request_id")
                        user_message = message.fields.get("message")
                        platform = message.fields.get("platform")
                        user_id = message.fields.get("user_id")
                        user_phone_number = message.fields.get("user_phone_number")
                        model_data = message.fields.get("model", {})
                        attachments = message.fields.get("attachments", [])
                        use_knowledge_base = message.fields.get(
                            "use_knowledge_base", False
                        )
                        use_search = message.fields.get("use_search", False)
                        agent_id = message.fields.get("agent_id")
                        use_memory_cross_conversations = message.fields.get(
                            "use_memory_cross_conversations", True
                        )
                        logging.info(f"model_data: {model_data}")
                        provider = model_data.get("provider", DEFAULT_PROVIDER)
                        model_name = model_data.get("name", DEFAULT_MODEL_NAME)
                        logging.info(
                            f"Using provider: {provider}, model: {model_name} for conversation {conversation_id}"
                        )

                        if not conversation_id or not user_message:
                            logging.warning(f"Invalid message format: {message.fields}")
                            await consumer.acknowledge_message(
                                message.stream, message.id
                            )
                            processing_successful = True
                            break

                        logging.info(
                            f"Processing message for conversation {conversation_id}: {int(time.time())}"
                        )

                        if platform == RequestPlatforms.WEB.value:
                            # Process the user message with the agent via web
                            async for formatted_chunk in run_agent_stream(
                                user_message, provider, model_name, attachments
                            ):
                                # Send the formatted chunk to the Redis Streams response stream
                                await streams_manager.producer.send_agent_response(
                                    conversation_id=conversation_id,
                                    response_data=formatted_chunk,
                                    request_id=request_id,
                                )
                            # Acknowledge the message after successful processing
                            ack_success = await consumer.acknowledge_message(
                                message.stream, message.id
                            )
                            if ack_success:
                                logging.info(
                                    f"Successfully processed and acknowledged message {message.id}"
                                )
                                processing_successful = True
                                retry_needed = False
                            else:
                                logging.warning(
                                    f"Failed to acknowledge message {message.id}"
                                )
                                retry_needed = False  # Don't retry ACK failures

                        elif platform == RequestPlatforms.SMS.value:
                            # Process the user message with the agent via sms
                            response = run_agent(user_message, provider, model_name)

                            inputTokens = response.metrics.accumulated_usage[
                                "inputTokens"
                            ]
                            outputTokens = response.metrics.accumulated_usage[
                                "outputTokens"
                            ]
                            totalTokens = response.metrics.accumulated_usage[
                                "totalTokens"
                            ]

                            usage_info = {
                                "inputTokens": inputTokens,
                                "outputTokens": outputTokens,
                                "totalTokens": totalTokens,
                            }

                            final_payload = {
                                "message": response.message["content"][-1]["text"],
                                "conversation_id": conversation_id,
                                "platform": platform,
                                "user_id": user_id,
                                "user_phone_number": user_phone_number,
                                "usage_info": usage_info,
                                "model": {provider: provider, model_name: model_name},
                            }

                            # Send final payload to gateway
                            async with aiohttp.ClientSession() as session:
                                headers = {"X-API-Key": settings.agent_gateway_api_key}
                                async with session.post(
                                    f"{settings.agent_gateway_url}/agents/response",
                                    json=final_payload,
                                    headers=headers,
                                ) as resp:
                                    if resp.status == 200:
                                        logging.info(
                                            "Successfully sent payload to gateway"
                                        )
                                    else:
                                        logging.error(
                                            f"Failed to send payload to gateway: {resp.status}"
                                        )
                            processing_successful = True
                            retry_needed = False

                        elif platform == RequestPlatforms.GLOBAL.value:
                            async for formatted_chunk in run_global_stream(
                                user_message=user_message,
                                provider=provider,
                                model=model_name,
                                use_knowledge_base=use_knowledge_base,
                                use_search=use_search,
                                user_id=user_id,
                                conversation_id=conversation_id,
                                agent_id=agent_id,
                                use_memory_cross_conversations=use_memory_cross_conversations,
                            ):
                                await streams_manager.producer.send_agent_response(
                                    conversation_id=conversation_id,
                                    response_data=formatted_chunk,
                                    request_id=request_id,
                                )
                            ack_success = await consumer.acknowledge_message(
                                message.stream, message.id
                            )
                            if ack_success:
                                logging.info(
                                    f"Successfully processed and acknowledged message {message.id}"
                                )
                                processing_successful = True
                                retry_needed = False
                            else:
                                logging.warning(
                                    f"Failed to acknowledge message {message.id}"
                                )
                                retry_needed = False

                        else:
                            logging.error(f"Unsupported platform: {platform}")
                            processing_successful = True
                            retry_needed = False

                    except Exception as e:
                        import traceback

                        logging.error(
                            f"Error processing message {message.id}: {traceback.format_exc()}"
                        )
                        logging.error(f"Error details: {e}")

                        # Use enhanced error handling
                        retry_needed = await consumer.handle_processing_error(
                            message, e, streams_manager
                        )

                        # Acknowledge the message after successful processing
                        ack_success = await consumer.acknowledge_message(
                            message.stream, message.id
                        )
                        if ack_success:
                            logging.info(
                                f"Successfully processed and acknowledged message {message.id}"
                            )
                            processing_successful = True
                            retry_needed = False
                        else:
                            logging.warning(
                                f"Failed to acknowledge message {message.id}"
                            )
                            retry_needed = False  # Don't retry ACK failures

            elif (
                message.stream
                == RedisWorkflowStreamEnum.WORKFLOW_REQUESTS.value.format(
                    env=settings.environment
                )
            ):
                while retry_needed:
                    try:
                        # Extract message data
                        request_id = message.fields.get("request_id")
                        prompt = message.fields.get("prompt")
                        if not prompt:
                            await consumer.acknowledge_message(
                                message.stream, message.id
                            )
                            processing_successful = True
                            break

                        # Process the user message with the agent
                        # async for formatted_chunk in run_agent_stream(
                        #     user_message, provider, model_name, attachments
                        # ):
                        #     # Send the formatted chunk to the Redis Streams response stream
                        #     await streams_manager.producer.send_agent_response(
                        #         conversation_id=conversation_id,
                        #         response_data=formatted_chunk,
                        #         request_id=request_id,
                        #     )
                        async for formatted_chunk in generate_workflow(
                            prompt, request_id
                        ):
                            await streams_manager.workflow_producer.send_agent_response(
                                response_data=formatted_chunk,
                                request_id=request_id,
                            )
                        # Acknowledge the message after successful processing
                        ack_success = await consumer.acknowledge_message(
                            message.stream, message.id
                        )
                        if ack_success:
                            processing_successful = True
                            retry_needed = False
                        else:
                            retry_needed = False  # Don't retry ACK failures

                    except Exception as e:
                        import traceback

                        logging.error(
                            f"Error processing message {message.id}: {traceback.format_exc()}"
                        )
                        logging.error(f"Error details: {e}")

                        # Use enhanced error handling
                        retry_needed = await consumer.handle_processing_error(
                            message, e, streams_manager
                        )

                        if not retry_needed:
                            processing_successful = False
                            break
            elif (
                message.stream
                == RedisWorkflowStreamEnum.WORKFLOW_CHAT_REQUESTS.value.format(
                    env=settings.environment
                )
            ):
                while retry_needed:
                    try:
                        # Extract message data
                        request_id = message.fields.get("request_id")
                        prompt = message.fields.get("prompt")
                        workflow = message.fields.get("workflow")
                        session_id = message.fields.get("session_id")
                        if not prompt:
                            await consumer.acknowledge_message(
                                message.stream, message.id
                            )
                            processing_successful = True
                            break

                        # Process the user message with the agent
                        # async for formatted_chunk in run_agent_stream(
                        #     user_message, provider, model_name, attachments
                        # ):
                        #     # Send the formatted chunk to the Redis Streams response stream
                        #     await streams_manager.producer.send_agent_response(
                        #         conversation_id=conversation_id,
                        #         response_data=formatted_chunk,
                        #         request_id=request_id,
                        #     )
                        async for formatted_chunk in workflow_chat(
                            prompt, workflow, session_id
                        ):
                            await streams_manager.workflow_producer.send_agent_response(
                                response_data=formatted_chunk,
                                request_id=request_id,
                                chat=True
                            )
                        # Acknowledge the message after successful processing
                        ack_success = await consumer.acknowledge_message(
                            message.stream, message.id
                        )
                        if ack_success:
                            processing_successful = True
                            retry_needed = False
                        else:
                            retry_needed = False  # Don't retry ACK failures

                    except Exception as e:
                        import traceback

                        logging.error(
                            f"Error processing message {message.id}: {traceback.format_exc()}"
                        )
                        logging.error(f"Error details: {e}")

                        # Use enhanced error handling
                        retry_needed = await consumer.handle_processing_error(
                            message, e, streams_manager
                        )

                        if not retry_needed:
                            processing_successful = False
                            break
            if not processing_successful:
                logging.error(
                    f"Failed to process message {message.id} after all retries"
                )

    except Exception as e:
        logging.error(f"Fatal error in Redis Streams listener: {e}")
        raise
    finally:
        logging.info(f"Stopping Redis Streams consumer {consumer_name}")
        consumer.stop()


async def listen_event_from_redis_pubsub():
    """Listen for events from Redis Streams"""
    logging.info("Using Redis Streams implementation")
    await listen_event_from_redis_streams()
