import json
from typing import Any, Dict, Optional

from app.shared.config.constants import EventType, SSEEventType

# Global storage for pending tool use
_pending_tool_use = None


def format_stream_chunk(chunk: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    global _pending_tool_use

    # Handle init_event_loop
    if "init_event_loop" in chunk and chunk["init_event_loop"]:
        return {"type": SSEEventType.STREAM_START.value}

    # Ignore these events
    if (
        ("start" in chunk and chunk["start"])
        or ("start_event_loop" in chunk and chunk["start_event_loop"])
        or ("event" in chunk and "messageStart" in chunk["event"])
    ):
        return None

    # Handle contentBlockStart - check for tool use first
    if "event" in chunk and "contentBlockStart" in chunk["event"]:
        start_content = chunk["event"]["contentBlockStart"]["start"]
        if "toolUse" in start_content:
            tool_name = start_content["toolUse"]["name"]
            return {"type": SSEEventType.TOOL_USE_START.value, "tool_name": tool_name}
        else:
            return {"type": SSEEventType.MESSAGE_START.value, "message_type": "text"}

    # Handle contentBlockDelta - check for tool use first
    if "event" in chunk and "contentBlockDelta" in chunk["event"]:
        delta = chunk["event"]["contentBlockDelta"]["delta"]
        if "toolUse" in delta:
            tool_input = delta["toolUse"]["input"]
            return {"type": SSEEventType.TOOL_CHUNK.value, "tool_delta": tool_input}
        elif "text" in delta:
            delta_text = delta["text"]
            return {"type": SSEEventType.CHUNK.value, "delta": delta_text}

    # Ignore these events (including tool_use stop events)
    if "event" in chunk and (
        "contentBlockStop" in chunk["event"]
        or "messageStop" in chunk["event"]
        or "metadata" in chunk["event"]
    ):
        return None

    # Handle message with text content
    if "message" in chunk and "content" in chunk["message"]:
        content = chunk["message"]["content"]
        if isinstance(content, list) and len(content) > 0:
            if "text" in content[0]:
                return {
                    "type": SSEEventType.TEXT_RESULT.value,
                    "result": {"type": "text", "content": content[0]["text"]},
                    "db_save": True,
                }
            elif "toolUse" in content[0]:
                # Store tool use info for potential pairing with tool result
                _pending_tool_use = content[0]["toolUse"]
                return None
            elif "toolResult" in content[0]:
                # Combine with pending tool use
                if _pending_tool_use:
                    tool_result = content[0]["toolResult"]
                    result = {
                        "type": SSEEventType.TOOL_RESULT.value,
                        "result": json.dumps(
                            {
                                "type": "tool",
                                "name": _pending_tool_use["name"],
                                "tool_input": _pending_tool_use["input"],
                                "tool_output": (
                                    {
                                        "text": tool_result["content"][0].get(
                                            "text", ""
                                        ),
                                    }
                                    if tool_result.get("content")
                                    else {"type": "tool"}
                                ),
                            }
                        ),
                        "db_save": True,
                    }
                    _pending_tool_use = None  # Reset
                    return result
                return None

    # Handle AgentResult
    if "result" in chunk and hasattr(chunk["result"], "metrics"):
        usage = chunk["result"].metrics.accumulated_usage
        return {
            "type": SSEEventType.STREAM_END.value,
            "usage_info": {
                "inputTokens": usage.get("inputTokens", 0),
                "outputTokens": usage.get("outputTokens", 0),
                "totalTokens": usage.get("totalTokens", 0),
            },
        }

    # Keep existing logic
    if "data" in chunk or "delta" in chunk:
        return None


def final_formatter(chunk):
    """
    Formats a LangGraph streaming chunk into a simplified output format.

    Args:
        chunk (dict): The raw event chunk from LangGraph stream

    Returns:
        dict or None: Formatted output with type, delta/source_agent, or None to ignore
    """
    # Handle dict-based events
    if isinstance(chunk, dict) and "event" in chunk:
        event = chunk["event"]

        # Ignore on_chain_start and on_chain_end events
        if event in ("on_chain_start", "on_chain_end"):
            return None

        # Handle on_chain_stream
        if event == "on_chain_stream":
            if "data" not in chunk or "chunk" not in chunk["data"]:
                return None

            data_chunk = chunk["data"]["chunk"]

            if isinstance(data_chunk, (list, tuple)) and len(data_chunk) >= 3:
                event_type = data_chunk[1]

                if event_type == "custom":
                    custom_data = data_chunk[2]

                    source_agent = "unknown"
                    if len(data_chunk) >= 1 and isinstance(
                        data_chunk[0], (list, tuple)
                    ):
                        namespace = data_chunk[0]
                        if len(namespace) > 0 and isinstance(namespace[0], str):
                            if ":" in namespace[0]:
                                source_agent = namespace[0].split(":")[0]

                    result = {
                        "type": EventType.TOOL_STREAM.value,
                        "delta": custom_data,
                        "source_agent": source_agent,
                    }

                    if (
                        isinstance(custom_data, dict)
                        and custom_data.get("type") == "workflow_complete"
                    ):
                        if custom_data.get("db_save"):
                            result["db_save"] = True

                    return result

            return None

        # Handle on_chat_model_start
        if event == "on_chat_model_start":
            source_agent = "unknown"
            if "metadata" in chunk and "langgraph_checkpoint_ns" in chunk["metadata"]:
                checkpoint_ns = chunk["metadata"]["langgraph_checkpoint_ns"]
                if ":" in checkpoint_ns:
                    source_agent = checkpoint_ns.split(":")[0]
            return {"type": EventType.MESSAGE_START.value, "source_agent": source_agent}

        # Handle on_chat_model_stream
        if event == "on_chat_model_stream":
            if "data" in chunk and "chunk" in chunk["data"]:
                message_chunk = chunk["data"]["chunk"]

                # Ignore chunks with tool_calls in additional_kwargs
                if (
                    hasattr(message_chunk, "additional_kwargs")
                    and "tool_calls" in message_chunk.additional_kwargs
                ):
                    return None

                # Extract delta from content
                delta = ""
                if hasattr(message_chunk, "content"):
                    delta = message_chunk.content if message_chunk.content else ""

                # Extract source_agent
                source_agent = "unknown"
                if (
                    "metadata" in chunk
                    and "langgraph_checkpoint_ns" in chunk["metadata"]
                ):
                    checkpoint_ns = chunk["metadata"]["langgraph_checkpoint_ns"]
                    if ":" in checkpoint_ns:
                        source_agent = checkpoint_ns.split(":")[0]

                return {
                    "type": EventType.MESSAGE.value,
                    "delta": delta,
                    "source_agent": source_agent,
                }

        # Handle on_tool_start
        if event == "on_tool_start":
            # Return None if no tool name
            if "name" not in chunk:
                return None

            tool_name = chunk["name"]
            tool_input = chunk.get("data", {}).get("input", {})

            source_agent = "unknown"
            if "metadata" in chunk and "langgraph_checkpoint_ns" in chunk["metadata"]:
                checkpoint_ns = chunk["metadata"]["langgraph_checkpoint_ns"]
                if ":" in checkpoint_ns:
                    source_agent = checkpoint_ns.split(":")[0]

            return {
                "type": EventType.TOOL_START.value,
                "tool_name": tool_name,
                "input": tool_input,
                "source_agent": source_agent,
            }

        # Handle on_tool_end
        if event == "on_tool_end":
            if "name" not in chunk:
                return None

            tool_name = chunk["name"]
            output = ""
            tool_id = ""

            # Extract output and tool_call_id from data.output
            if "data" in chunk and "output" in chunk["data"]:
                data_output = chunk["data"]["output"]

                # Case 1: output is a ToolMessage directly
                if hasattr(data_output, "content"):
                    output = data_output.content
                    if hasattr(data_output, "tool_call_id"):
                        tool_id = data_output.tool_call_id
                # Case 2: output is a Command with update containing messages
                elif (
                    hasattr(data_output, "update") and "messages" in data_output.update
                ):
                    messages = data_output.update["messages"]
                    if isinstance(messages, list) and len(messages) > 0:
                        # Get the first ToolMessage content and tool_call_id
                        if hasattr(messages[0], "content"):
                            output = messages[0].content
                        if hasattr(messages[0], "tool_call_id"):
                            tool_id = messages[0].tool_call_id

            source_agent = "unknown"
            if "metadata" in chunk and "langgraph_checkpoint_ns" in chunk["metadata"]:
                checkpoint_ns = chunk["metadata"]["langgraph_checkpoint_ns"]
                if ":" in checkpoint_ns:
                    source_agent = checkpoint_ns.split(":")[0]

            return {
                "type": EventType.TOOL_END.value,
                "tool_name": tool_name,
                "output": output,
                "tool_id": tool_id,
                "source_agent": source_agent,
                "db_save": True,
            }

        # Handle on_chat_model_end
        if event == "on_chat_model_end":
            if "data" not in chunk or "output" not in chunk["data"]:
                return None

            ai_message = chunk["data"]["output"]

            # Extract text content
            text_content = ""
            if hasattr(ai_message, "content"):
                text_content = ai_message.content if ai_message.content else ""

            # Extract tool_call data
            tool_call = {}
            if hasattr(ai_message, "tool_calls") and ai_message.tool_calls:
                first_tool_call = ai_message.tool_calls[0]
                tool_call = {
                    "tool_name": first_tool_call.get("name", ""),
                    "tool_id": first_tool_call.get("id", ""),
                    "tool_input": first_tool_call.get("args", {}),
                }

            # Extract usage data
            usage_data = {}
            if hasattr(ai_message, "usage_metadata"):
                usage_metadata = ai_message.usage_metadata
                usage_data = {
                    "input_tokens": usage_metadata.get("input_tokens", 0),
                    "output_tokens": usage_metadata.get("output_tokens", 0),
                    "total_tokens": usage_metadata.get("total_tokens", 0),
                }

            # Extract source_agent
            source_agent = "unknown"
            if "metadata" in chunk and "langgraph_checkpoint_ns" in chunk["metadata"]:
                checkpoint_ns = chunk["metadata"]["langgraph_checkpoint_ns"]
                if ":" in checkpoint_ns:
                    source_agent = checkpoint_ns.split(":")[0]

            return {
                "type": EventType.MESSAGE_END.value,
                "text_content": text_content,
                "tool_call": tool_call,
                "usage_data": usage_data,
                "source_agent": source_agent,
                "db_save": True,
            }

    # Legacy tuple-based format handling
    # Default values
    delta = ""
    source_agent = "unknown"

    # Check if chunk is a tuple with at least 3 elements
    if not isinstance(chunk, tuple) or len(chunk) < 3:
        return {
            "type": EventType.MESSAGE.value,
            "delta": delta,
            "source_agent": source_agent,
        }

    namespace_tuple, event_type, data = chunk

    # Extract delta from 'messages' events
    if event_type == "messages" and isinstance(data, tuple) and len(data) >= 1:
        message_chunk = data[0]
        # Check if it's an AIMessageChunk with content
        if hasattr(message_chunk, "content"):
            delta = message_chunk.content if message_chunk.content else ""

        # Extract source_agent from metadata (second element of data tuple)
        if len(data) >= 2 and isinstance(data[1], dict):
            metadata = data[1]
            if "langgraph_checkpoint_ns" in metadata:
                checkpoint_ns = metadata["langgraph_checkpoint_ns"]
                # Extract the first agent name before the colon
                if ":" in checkpoint_ns:
                    source_agent = checkpoint_ns.split(":")[0]

    # Also try to extract from namespace_tuple
    if source_agent == "unknown" and namespace_tuple:
        if isinstance(namespace_tuple, tuple) and len(namespace_tuple) > 0:
            first_ns = namespace_tuple[0]
            if isinstance(first_ns, str) and ":" in first_ns:
                source_agent = first_ns.split(":")[0]

    return {
        "type": EventType.MESSAGE.value,
        "delta": delta,
        "source_agent": source_agent,
    }
